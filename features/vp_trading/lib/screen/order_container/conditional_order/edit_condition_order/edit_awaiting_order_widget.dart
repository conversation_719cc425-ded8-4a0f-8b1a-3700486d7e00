import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/order_edit/edit_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/screen/order_container/conditional_order/edit_condition_order/edit_effective_time_widget.dart';
import 'package:vp_trading/screen/place_order/awaiting/activation_conditions_widget.dart';
import 'package:vp_trading/screen/place_order/awaiting/awaiting_price_volume_widget.dart';
import 'package:vp_trading/screen/place_order/widgets/choice_dilution_action_widget.dart';

class EditAwaitingOrderWidget extends StatefulWidget {
  const EditAwaitingOrderWidget({
    super.key,
    required this.item,
    required this.onEditSuccess,
  });

  final ConditionOrderBookModel item;
  final VoidCallback onEditSuccess;

  @override
  State<EditAwaitingOrderWidget> createState() =>
      _EditAwaitingOrderWidgetState();
}

class _EditAwaitingOrderWidgetState extends State<EditAwaitingOrderWidget> {
  late final TextEditingController _activationPriceController;

  final _activationPriceKey = GlobalKey<ActivationConditionsWidgetState>();

  @override
  void initState() {
    super.initState();

    _activationPriceController = TextEditingController(
      text:
          widget.item.activePrice != null
              ? (widget.item.activePrice! / 1000).toString()
              : '',
    );
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeState();
    });
  }

  void _initializeState() {
    final activationType = _parseActivationType(widget.item.activeType);
    context.read<ValidateConditionOrderCubit>().setActivationConditions(
      activationType,
    );

    if (_activationPriceController.text.isNotEmpty) {
      context.read<ValidateOrderCubit>().onChangeActivationPrice(
        _activationPriceController.text,
      );
    }

    if (widget.item.price != null && widget.item.price!.isNotEmpty) {
      final priceValue = double.tryParse(widget.item.price!) ?? 0;
      final displayPrice = (priceValue / 1000).toString();
      context.read<ValidateOrderCubit>().onChangePrice(displayPrice);
    }
    if (widget.item.qty != null) {
      context.read<ValidateOrderCubit>().onChangeVolumne(
        widget.item.qty?.toInt().toString() ?? "0",
      );
    }
  }

  ActivationConditionsType _parseActivationType(String? activeType) {
    switch (activeType?.toUpperCase()) {
      case 'GE':
      case 'GREATER_THAN':
        return ActivationConditionsType.greaterThan;
      case 'LE':
      case 'LESS_THAN':
      default:
        return ActivationConditionsType.lessThan;
    }
  }

  DateTime? _parseDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;

    try {
      // Assuming date format is dd/mm/yyyy or similar
      final parts = dateString.split('/');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    } catch (e) {
      // Handle parsing error
    }
    return null;
  }

  bool _hasFieldsChanged() {
    final validateOrderState = context.read<ValidateOrderCubit>().state;
    final validateConditionState =
        context.read<ValidateConditionOrderCubit>().state;

    // Compare original values with current values (using server format for accurate comparison)
    final originalActivationPrice = widget.item.activePrice ?? 0.0;
    final originalPrice =
        widget.item.price != null && widget.item.price!.isNotEmpty
            ? double.tryParse(widget.item.price!) ?? 0.0
            : 0.0;
    final originalVolume = widget.item.qty?.toInt() ?? 0;
    final originalActiveType = _parseActivationType(widget.item.activeType);

    // Handle effective time comparison properly
    final originalFromDate = widget.item.fromDate ?? '';
    final originalToDate = widget.item.toDate ?? '';

    // If original dates are empty, use current date as default for comparison
    final normalizedOriginalFromDate =
        originalFromDate.isEmpty
            ? DateTime.now().formatToDdMmYyyy()
            : originalFromDate;
    final normalizedOriginalToDate =
        originalToDate.isEmpty
            ? DateTime.now().formatToDdMmYyyy()
            : originalToDate;

    final currentActivationPrice = _activationPriceController.text.price ?? 0.0;
    final currentPrice = validateOrderState.currentPrice?.price ?? 0.0;
    final currentVolume =
        int.tryParse(validateOrderState.currentVolume ?? '0') ?? 0;
    final currentActiveType = validateConditionState.activationType;
    final currentFromDate = validateConditionState.fromDate ?? '';
    final currentToDate = validateConditionState.toDate ?? '';

    return originalActivationPrice != currentActivationPrice ||
        originalPrice != currentPrice ||
        originalVolume != currentVolume ||
        originalActiveType != currentActiveType ||
        normalizedOriginalFromDate != currentFromDate ||
        normalizedOriginalToDate != currentToDate;
  }

  void _handleSave() {
    final validateOrderState = context.read<ValidateOrderCubit>().state;
    final validateConditionState =
        context.read<ValidateConditionOrderCubit>().state;

    // Check if all validations pass
    if (validateOrderState.errorActivationPrice.isError ||
        validateOrderState.errorPrice.isError ||
        validateOrderState.errorVolume.isError) {
      // Show validation errors
      context.showSnackBar(
        content: 'Vui lòng nhập thông tin hợp lệ',
        snackBarType: VPSnackBarType.error,
      );
      return;
    }

    // Get current values
    // Note: Convert display prices (divided by 1000) back to server format (multiply by 1000)
    final activePrice = _activationPriceController.text.price ?? 0.0;
    final price = validateOrderState.currentPrice?.price ?? 0.0;
    final qty =
        (double.tryParse(validateOrderState.currentVolume ?? '0') ?? 0.0)
            .round();
    final activeType = validateConditionState.activationType.toParamRequest();

    // Call update API
    context.read<EditConditionOrderCubit>().updateConditionOrder(
      request: ConditionOrderRequestModel(
        requestId: "app_${AppHelper().genXRequestID()}",
        orderType: widget.item.orderType ?? '',
        accountId: widget.item.accountId ?? '',
        orderId: widget.item.orderId ?? '',
        conditionInfo: ConditionInfo(
          symbol: widget.item.symbol ?? '',
          qty: qty,
          side: widget.item.side?.toLowerCase() ?? '',
          type: "limit",
          price: price,
          fromDate: validateConditionState.fromDate ?? '',
          toDate: validateConditionState.toDate ?? '',
          activePrice: activePrice,
          activeType: activeType,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _activationPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<EditConditionOrderCubit, EditConditionOrderState>(
      listener: (context, state) {
        if (state.status == EditConditionOrderStatus.success) {
          widget.onEditSuccess();
        } else if (state.status == EditConditionOrderStatus.failure) {
          // Show error message
          context.showSnackBar(
            content: state.errorMessage ?? "-",
            snackBarType: VPSnackBarType.error,
          );
        }
      },
      child: Column(
        children: [
          // Activation Conditions
          ActivationConditionsWidget(
            key: _activationPriceKey,
            priceController: _activationPriceController,
          ),

          // Price and Volume
          const AwaitingPriceVolumeWidget(),

          const SizedBox(height: 8),

          // Effective Time
          EditEffectiveTimeWidget(
            initialFromDate: _parseDate(widget.item.fromDate),
            initialToDate: _parseDate(widget.item.toDate),
          ),

          const SizedBox(height: 8),

          // Dilution Action
          const ChoiceDilutionActionWidget(),

          const SizedBox(height: 24),

          // Action Buttons
          BlocBuilder<EditConditionOrderCubit, EditConditionOrderState>(
            builder: (context, editState) {
              return BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
                builder: (context, validateOrderState) {
                  return BlocBuilder<
                    ValidateConditionOrderCubit,
                    ValidateConditionOrderState
                  >(
                    builder: (context, validateConditionState) {
                      final hasChanges = _hasFieldsChanged();
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: VpsButton.primaryXsSmall(
                          title: "Sửa lệnh",
                          onPressed:
                              editState.status ==
                                          EditConditionOrderStatus.loading ||
                                      !hasChanges
                                  ? null
                                  : _handleSave,
                          alignment: Alignment.center,
                          disabled:
                              editState.status ==
                                  EditConditionOrderStatus.loading ||
                              !hasChanges ||
                              (validateOrderState
                                      .errorActivationPrice
                                      .isError ||
                                  validateOrderState.errorPrice.isError ||
                                  validateOrderState.errorVolume.isError),
                        ),
                      );
                    },
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }
}
