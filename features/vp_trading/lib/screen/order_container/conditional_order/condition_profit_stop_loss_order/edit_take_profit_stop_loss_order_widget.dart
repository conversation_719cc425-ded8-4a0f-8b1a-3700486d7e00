import 'package:flutter/material.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/order_edit/edit_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/model/enum/take_profit_trigger_condition_enum.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/screen/order_container/conditional_order/edit_condition_order/edit_effective_time_widget.dart';
import 'package:vp_trading/screen/order_container/enum/condition_order_type_enum.dart';
import 'package:vp_trading/screen/place_order/take_profit_stop_loss/take_profit_stop_loss_volume_widget.dart';
import 'package:vp_trading/screen/place_order/take_profit_stop_loss/widget/slippage_text_input_field.dart';
import 'package:vp_trading/screen/place_order/take_profit_stop_loss/widget/take_profit_choice_trigger_condition.dart';
import 'package:vp_trading/screen/place_order/take_profit_stop_loss/widget/trigger_text_input_field.dart';
import 'package:vp_trading/screen/place_order/widgets/available_trade/condition_available_trade_label.dart';
import 'package:vp_trading/screen/place_order/widgets/choice_dilution_action_widget.dart';

class EditTakeProfitStopLossOrderWidget extends StatefulWidget {
  const EditTakeProfitStopLossOrderWidget({
    super.key,
    required this.item,
    required this.onEditSuccess,
  });

  final ConditionOrderBookModel item;
  final VoidCallback onEditSuccess;

  @override
  State<EditTakeProfitStopLossOrderWidget> createState() =>
      _EditTakeProfitStopLossOrderWidgetState();
}

class _EditTakeProfitStopLossOrderWidgetState
    extends State<EditTakeProfitStopLossOrderWidget> {
  final _triggerController = TextEditingController();
  final _slippageController = TextEditingController();
  final _volumeController = TextEditingController();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeState();
    });
  }

  void _initializeState() {
    String? triggerValueStr;
    TakeProfitTriggerConditionEnum triggerCondition =
        TakeProfitTriggerConditionEnum.slippageProfit;

    // For take profit orders (TPO)
    if (widget.item.conditionOrderTypeEnum == ConditionOrderTypeEnum.tpo) {
      if (widget.item.activepriceTP != null && widget.item.activepriceTP! > 0) {
        triggerValueStr = widget.item.activepriceTP.toString();
        triggerCondition =
            TakeProfitTriggerConditionEnum.slippageProfit; // Price-based
      } else if (widget.item.priceTP != null &&
          widget.item.priceTP!.isNotEmpty) {
        triggerValueStr = widget.item.priceTP!;
        triggerCondition =
            TakeProfitTriggerConditionEnum.slippageProfit; // Price-based
      }
    }
    // For stop loss orders (SLO)
    else if (widget.item.conditionOrderTypeEnum == ConditionOrderTypeEnum.slo) {
      if (widget.item.activepriceSL != null && widget.item.activepriceSL! > 0) {
        triggerValueStr = widget.item.activepriceSL.toString();
        triggerCondition =
            TakeProfitTriggerConditionEnum.slippageProfit; // Price-based
      } else if (widget.item.priceSL != null &&
          widget.item.priceSL!.isNotEmpty) {
        triggerValueStr = widget.item.priceSL!;
        triggerCondition =
            TakeProfitTriggerConditionEnum.slippageProfit; // Price-based
      }
    }

    // Fallback to general fields
    if (triggerValueStr == null) {
      if (widget.item.activePrice != null && widget.item.activePrice! > 0) {
        triggerValueStr = widget.item.activePrice.toString();
        triggerCondition =
            TakeProfitTriggerConditionEnum.slippageProfit; // Price-based
      } else if (widget.item.price != null && widget.item.price!.isNotEmpty) {
        triggerValueStr = widget.item.price!;
        triggerCondition =
            TakeProfitTriggerConditionEnum.slippageProfit; // Price-based
      } else if (widget.item.stopPrice != null &&
          widget.item.stopPrice!.isNotEmpty) {
        triggerValueStr = widget.item.stopPrice!;
        triggerCondition =
            TakeProfitTriggerConditionEnum.slippageProfit; // Price-based
      }
    }

    if (triggerValueStr != null) {
      final triggerValue = double.tryParse(triggerValueStr) ?? 0;

      String displayTrigger;
      if (triggerValue > 1000) {
        displayTrigger = (triggerValue / 1000).toString();
      } else {
        displayTrigger = triggerValue.toString();
      }

      _triggerController.text = displayTrigger;

      // Set trigger condition type
      context.read<ValidateConditionOrderCubit>().setTriggerConditionEnum(
        triggerCondition,
      );

      context.read<ValidateConditionOrderCubit>().onChangeTrigger(
        displayTrigger,
      );

      final orderType =
          widget.item.conditionOrderTypeEnum == ConditionOrderTypeEnum.tpo
              ? OrderType.takeProfit
              : OrderType.stopLoss;
      context.read<PlaceOrderCubit>().updateOrderType(orderType);
    }

    // Pre-fill slippage value
    if (widget.item.slipPagePrice != null) {
      _slippageController.text = widget.item.slipPagePrice!.toString();
      context.read<ValidateConditionOrderCubit>().onChangeSlippage(
        widget.item.slipPagePrice!.toString(),
      );
    }

    // Pre-fill volume
    if (widget.item.qty != null) {
      final volumeStr = MoneyUtils.formatMoney(
        (widget.item.qty ?? 0).toDouble(),
        suffix: '',
      );
      _volumeController.text = volumeStr;
      context.read<ValidateOrderCubit>().onChangeVolumne(volumeStr);
    }
  }

  DateTime? _parseDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    try {
      final parts = dateString.split('/');
      if (parts.length == 3) {
        return DateTime(
          int.parse(parts[2]),
          int.parse(parts[1]),
          int.parse(parts[0]),
        );
      }
    } catch (e) {
      // Invalid date format
    }
    return null;
  }

  bool _hasFieldsChanged() {
    // Get original values based on order type (using server format for accurate comparison)
    double originalTriggerValue = 0.0;
    double originalSlippageValue = 0.0;
    int originalVolume = 0;

    // For take profit orders (TPO)
    if (widget.item.conditionOrderTypeEnum == ConditionOrderTypeEnum.tpo) {
      if (widget.item.activepriceTP != null && widget.item.activepriceTP! > 0) {
        originalTriggerValue = widget.item.activepriceTP!.toDouble();
      } else if (widget.item.priceTP != null &&
          widget.item.priceTP!.isNotEmpty) {
        originalTriggerValue = double.tryParse(widget.item.priceTP!) ?? 0.0;
      }
    }
    // For stop loss orders (SLO)
    else if (widget.item.conditionOrderTypeEnum == ConditionOrderTypeEnum.slo) {
      if (widget.item.activepriceSL != null && widget.item.activepriceSL! > 0) {
        originalTriggerValue = widget.item.activepriceSL!.toDouble();
      } else if (widget.item.priceSL != null &&
          widget.item.priceSL!.isNotEmpty) {
        originalTriggerValue = double.tryParse(widget.item.priceSL!) ?? 0.0;
      }
    }

    // Fallback to general fields if specific fields are not available
    if (originalTriggerValue == 0.0) {
      if (widget.item.activePrice != null && widget.item.activePrice! > 0) {
        originalTriggerValue = widget.item.activePrice!.toDouble();
      } else if (widget.item.price != null && widget.item.price!.isNotEmpty) {
        originalTriggerValue = double.tryParse(widget.item.price!) ?? 0.0;
      } else if (widget.item.stopPrice != null &&
          widget.item.stopPrice!.isNotEmpty) {
        originalTriggerValue = double.tryParse(widget.item.stopPrice!) ?? 0.0;
      }
    }

    originalSlippageValue = (widget.item.slipPagePrice ?? 0.0).toDouble();
    originalVolume = widget.item.qty?.toInt() ?? 0;

    // Get current values (using price extension to convert to server format)
    final validateConditionState =
        context.read<ValidateConditionOrderCubit>().state;
    final currentTriggerValue = _triggerController.text.price ?? 0.0;
    final currentSlippageValue =
        double.tryParse(_slippageController.text) ?? 0.0;
    final currentVolume = _volumeController.text.volume.toInt();

    // Compare effective time
    final originalFromDate = widget.item.fromDate ?? '';
    final originalToDate = widget.item.toDate ?? '';
    final currentFromDate = validateConditionState.fromDate ?? '';
    final currentToDate = validateConditionState.toDate ?? '';

    // Handle date comparison logic:
    // - If original dates are empty, EditEffectiveTimeWidget sets current dates to today
    // - If original dates have values, compare them directly with current dates
    // - Only consider dates changed if original had values and current is different

    bool datesChanged = false;

    if (originalFromDate.isNotEmpty) {
      // Original had a from date, compare with current
      datesChanged = originalFromDate != currentFromDate;
    }

    if (!datesChanged && originalToDate.isNotEmpty) {
      // Original had a to date, compare with current
      datesChanged = originalToDate != currentToDate;
    }

    // If both original dates were empty, EditEffectiveTimeWidget will set current dates
    // to today's date, but this should not be considered a change since user didn't
    // explicitly modify the dates

    return originalTriggerValue != currentTriggerValue ||
        originalSlippageValue != currentSlippageValue ||
        originalVolume != currentVolume ||
        datesChanged;
  }

  void _handleSave() {
    final validateConditionState =
        context.read<ValidateConditionOrderCubit>().state;

    final triggerValue = double.tryParse(_triggerController.text) ?? 0;
    final slippageValue = double.tryParse(_slippageController.text) ?? 0;
    final qty = _volumeController.text.volume.toInt();

    final serverTriggerValue = (_triggerController.text.price ?? 0.0).round();

    // Calculate the actual order price based on trigger condition and order type
    final orderPrice = validateConditionState.orderPriceAbs;

    // Call update API
    context.read<EditConditionOrderCubit>().updateConditionOrder(
      request: ConditionOrderRequestModel(
        requestId: "app_${AppHelper().genXRequestID()}",
        orderType: widget.item.orderType ?? '',
        accountId: widget.item.accountId ?? '',
        orderId: widget.item.orderId ?? '',
        conditionInfo: ConditionInfo(
          symbol: widget.item.symbol ?? '',
          qty: qty,
          side: widget.item.side?.toLowerCase() ?? '',
          type: "limit",
          price:
              orderPrice, // Use calculated order price instead of trigger value
          fromDate: validateConditionState.fromDate ?? '',
          toDate: validateConditionState.toDate ?? '',
          slipPagePrice: slippageValue,
          stopLossRate:
              validateConditionState.triggerConditionEnum ==
                      TakeProfitTriggerConditionEnum.rateProfit
                  ? triggerValue
                  : null,
          stopLossPriceAmp:
              validateConditionState.triggerConditionEnum ==
                      TakeProfitTriggerConditionEnum.slippageProfit
                  ? serverTriggerValue
                  : null,
          activeType:
              widget.item.conditionOrderTypeEnum == ConditionOrderTypeEnum.tpo
                  ? ActivationConditionsType.greaterThan.toParamRequest()
                  : ActivationConditionsType.lessThan.toParamRequest(),
          costPrice: validateConditionState.costPrice,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _triggerController.dispose();
    _slippageController.dispose();
    _volumeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<EditConditionOrderCubit, EditConditionOrderState>(
      listener: (context, state) {
        if (state.status == EditConditionOrderStatus.success) {
          widget.onEditSuccess();
        } else if (state.status == EditConditionOrderStatus.failure) {
          // Show error message
          context.showSnackBar(
            content: state.errorMessage ?? "-",
            snackBarType: VPSnackBarType.error,
          );
        }
      },
      child: Column(
        children: [
          // Available Trade Label (only for sell orders)
          BlocSelector<PlaceOrderCubit, PlaceOrderState, OrderAction?>(
            selector: (state) => state.action,
            builder: (_, action) {
              if (action == OrderAction.buy) {
                return const SizedBox.shrink();
              }
              return const ConditionAvailableTradeLabel();
            },
          ),

          const SizedBox(height: 8),

          // Trigger Component
          _buildTriggerComponent(),

          const SizedBox(height: 8),

          // Slippage Input
          SlippageTextInputField(slippageController: _slippageController),

          const SizedBox(height: 8),

          // Order Price Display
          _buildOrderPriceDisplay(),

          // Volume Input
          TakeProfitStopLossVolumeWidget(volumeController: _volumeController),

          const SizedBox(height: 8),

          // Effective Time
          EditEffectiveTimeWidget(
            initialFromDate: _parseDate(widget.item.fromDate),
            initialToDate: _parseDate(widget.item.toDate),
          ),

          const SizedBox(height: 8),

          // Dilution Action
          const ChoiceDilutionActionWidget(),

          const SizedBox(height: 24),

          // Action Buttons
          BlocBuilder<EditConditionOrderCubit, EditConditionOrderState>(
            builder: (context, editState) {
              return BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
                builder: (context, validateOrderState) {
                  return BlocBuilder<
                    ValidateConditionOrderCubit,
                    ValidateConditionOrderState
                  >(
                    builder: (context, validateConditionState) {
                      final hasChanges = _hasFieldsChanged();

                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: VpsButton.primaryXsSmall(
                          title: "Sửa lệnh",
                          onPressed:
                              editState.status ==
                                          EditConditionOrderStatus.loading ||
                                      !hasChanges
                                  ? null
                                  : _handleSave,
                          alignment: Alignment.center,
                          disabled:
                              editState.status ==
                                  EditConditionOrderStatus.loading ||
                              !hasChanges ||
                              (validateConditionState.errorTrigger.isError ||
                                  validateConditionState
                                      .errorSlippage
                                      .isError ||
                                  validateOrderState.errorVolume.isError),
                        ),
                      );
                    },
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTriggerComponent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Điều kiện kích hoạt",
            style: context.textStyle.subtitle14?.copyWith(
              color: vpColor.textPrimary,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: BlocListener<
                  ValidateConditionOrderCubit,
                  ValidateConditionOrderState
                >(
                  listener: (context, state) {
                    // Listener for state changes
                  },
                  child: const TakeProfitChoiceTriggerCondition(),
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: TriggerTextInputField(
                  triggerController: _triggerController,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderPriceDisplay() {
    return BlocBuilder<
      ValidateConditionOrderCubit,
      ValidateConditionOrderState
    >(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                VPTradingLocalize.current.trading_order_price,
                style: context.textStyle.body14?.copyWith(
                  color: vpColor.textPrimary,
                ),
              ),
              Text(
                state.orderPriceDisplay,
                style: context.textStyle.subtitle14?.copyWith(
                  color: vpColor.textPriceGreen,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
